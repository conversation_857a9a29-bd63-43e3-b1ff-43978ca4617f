import ssl
from vllm.engine.arg_utils import AsyncEngineArgs, FlexibleArgumentParser


parser = FlexibleArgumentParser()
parser.add_argument("--host", type=str, default=None)  # 服务器主机地址
parser.add_argument("--port", type=parser.check_port, default=8000)  # 服务器端口，使用parser.check_port检查端口是否可用
parser.add_argument("--ssl-keyfile", type=str, default=None) # SSL密钥文件
parser.add_argument("--ssl-certfile", type=str, default=None) # SSL证书文件
parser.add_argument("--ssl-ca-certs",
                    type=str,
                    default=None,
                    help="The CA certificates file") # CA证书文件
parser.add_argument(
    "--enable-ssl-refresh",
    action="store_true",
    default=False,
    help="Refresh SSL Context when SSL certificate files change") # 当SSL证书文件改变时，刷新SSL上下文
parser.add_argument(
    "--ssl-cert-reqs",
    type=int,
    default=int(ssl.CERT_NONE),
    help="Whether client certificate is required (see stdlib ssl module's)") # 客户端证书要求
parser.add_argument(
    "--root-path",
    type=str,
    default=None,
    help="FastAPI root_path when app is behind a path based routing proxy") # FastAPI root_path，当app在基于路径的路由代理后面时
parser.add_argument("--log-level", type=str, default="debug") # 日志级别
parser = AsyncEngineArgs.add_cli_args(parser)