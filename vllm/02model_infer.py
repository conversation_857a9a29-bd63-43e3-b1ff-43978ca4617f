from vllm import LLM, SamplingParams


llm = LLM(
    model="/home/<USER>/wangsaner/Weights/Qwen/Qwen3-0.6B",
    trust_remote_code=True,
    gpu_memory_utilization=0.8,
    tensor_parallel_size=1,
)


sampling_params = SamplingParams(
    max_tokens=8192,
    temperature=0.6,
    top_p=0.95,
    # 防止重复的关键参数
    repetition_penalty=1.1,  # 重复惩罚，值越大越不容易重复，建议1.0-1.2
    frequency_penalty=0.1,   # 频率惩罚，减少高频词重复
    presence_penalty=0.1,    # 存在惩罚，鼓励生成新词汇
    # 其他有用参数
    top_k=50,               # 限制候选词数量
    min_tokens=10,          # 最少生成token数
)


text = [
    "请详细解释什么是黑洞，包括其形成原理、主要特征和科学意义。请用清晰、有条理的方式回答，避免重复表述。"
]

outputs = llm.generate(text, sampling_params=sampling_params)

for output in outputs:
    generate_text = output.outputs[0].text
    print("generate text:", generate_text)