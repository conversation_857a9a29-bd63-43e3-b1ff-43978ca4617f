#!/usr/bin/env python3
"""
简单的 LLM API 服务器
使用 Transformers + FastAPI 替代 vLLM
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig
from fastapi import FastAPI, HTTPException, Depends, Header
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 模型路径
MODEL_PATH = "/home/<USER>/wangsaner/Weights/Qwen/Qwen3-0.6B"

# 全局变量
model = None
tokenizer = None

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    max_tokens: Optional[int] = 512
    temperature: Optional[float] = 0.7
    top_p: Optional[float] = 0.9
    stream: Optional[bool] = False

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[dict]
    usage: dict

# FastAPI 应用
app = FastAPI(title="Simple LLM API", version="1.0.0")

def verify_api_key(authorization: str = Header(None)):
    """验证 API Key"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Missing Authorization header")
    
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid Authorization header format")
    
    api_key = authorization[7:]  # 移除 "Bearer " 前缀
    if api_key != "muyu":
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return api_key

def load_model():
    """加载模型和分词器"""
    global model, tokenizer
    
    logger.info(f"Loading model from {MODEL_PATH}")
    
    try:
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(
            MODEL_PATH,
            trust_remote_code=True,
            padding_side="left"
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # 加载模型
        model = AutoModelForCausalLM.from_pretrained(
            MODEL_PATH,
            trust_remote_code=True,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            low_cpu_mem_usage=True
        )
        
        logger.info("Model loaded successfully")
        
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        raise

def generate_response(messages: List[ChatMessage], max_tokens: int, temperature: float, top_p: float) -> str:
    """生成回复"""
    global model, tokenizer
    
    # 构建提示词
    prompt = ""
    for msg in messages:
        if msg.role == "user":
            prompt += f"用户: {msg.content}\n"
        elif msg.role == "assistant":
            prompt += f"助手: {msg.content}\n"
        elif msg.role == "system":
            prompt += f"系统: {msg.content}\n"
    
    prompt += "助手: "
    
    # 生成配置
    generation_config = GenerationConfig(
        max_new_tokens=max_tokens,
        temperature=temperature,
        top_p=top_p,
        top_k=50,
        repetition_penalty=1.1,
        no_repeat_ngram_size=3,
        do_sample=True,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=tokenizer.eos_token_id,
    )
    
    # 编码输入
    inputs = tokenizer(prompt, return_tensors="pt", padding=True)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    # 生成
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            generation_config=generation_config,
        )
    
    # 解码输出
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # 提取回复部分
    if "助手: " in generated_text:
        response = generated_text.split("助手: ")[-1].strip()
    else:
        response = generated_text.replace(prompt, "").strip()
    
    return response

@app.on_event("startup")
async def startup_event():
    """启动时加载模型"""
    load_model()

@app.get("/")
async def root():
    """根路径"""
    return {"message": "Simple LLM API Server", "status": "running"}

@app.get("/v1/models")
async def list_models(api_key: str = Depends(verify_api_key)):
    """列出可用模型"""
    return {
        "object": "list",
        "data": [
            {
                "id": "qwen3-0.6b",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "local"
            }
        ]
    }

@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest,
    api_key: str = Depends(verify_api_key)
):
    """聊天完成接口"""
    try:
        logger.info(f"Received chat completion request: {request.model}")
        
        # 生成回复
        response_text = generate_response(
            request.messages,
            request.max_tokens or 512,
            request.temperature or 0.7,
            request.top_p or 0.9
        )
        
        # 构建响应
        response = ChatCompletionResponse(
            id=f"chatcmpl-{int(time.time())}",
            created=int(time.time()),
            model=request.model,
            choices=[
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_text
                    },
                    "finish_reason": "stop"
                }
            ],
            usage={
                "prompt_tokens": 0,  # 简化实现
                "completion_tokens": 0,
                "total_tokens": 0
            }
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("启动简单 LLM API 服务器...")
    print(f"模型路径: {MODEL_PATH}")
    print("端口: 9000")
    print("API Key: muyu")
    print("=" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=9000,
        log_level="info"
    )
