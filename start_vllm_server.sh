#!/bin/bash

# vLLM 服务器启动脚本
# 使用 Qwen2.5-7B-Instruct 模型

echo "启动 vLLM 服务器..."
echo "模型: Qwen3-0.6B"
echo "端口: 9000"
echo "API Key: muyu"
echo "================================"

# 检查 GPU 状态
echo "检查 GPU 状态:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits

# 检查模型路径
echo ""
echo "检查模型路径:"
MODEL_PATH="/home/<USER>/wangsaner/Weights/Qwen/Qwen3-0.6B"
if [ -d "$MODEL_PATH" ]; then
    echo "✓ 模型目录存在: $MODEL_PATH"
    ls -la "$MODEL_PATH" | head -5
    echo "✓ 检查 config.json:"
    if [ -f "$MODEL_PATH/config.json" ]; then
        echo "✓ config.json 存在"
    else
        echo "❌ config.json 不存在"
    fi
else
    echo "❌ 模型路径不存在: $MODEL_PATH"
    exit 1
fi

echo ""
echo "================================"
echo "启动服务器..."

# 启动服务器 - 使用正确的模型路径
vllm serve /home/<USER>/wangsaner/Weights/Qwen/Qwen3-0.6B \
  --served-model-name qwen3-0.6b \
  --api_key muyu \
  --host 0.0.0.0 \
  --port 9000 \
  --trust_remote_code \
  --max_model_len 2048 \
  --gpu_memory_utilization 0.8

echo ""
echo "服务器启动完成！"
echo "访问地址: http://**************:9000"
echo "API 文档: http://**************:9000/docs"
echo "测试命令: curl -X POST http://**************:9000/v1/chat/completions -H 'Authorization: Bearer muyu' -H 'Content-Type: application/json' -d '{\"model\": \"qwen3-0.6b\", \"messages\": [{\"role\": \"user\", \"content\": \"Hello\"}]}'"
