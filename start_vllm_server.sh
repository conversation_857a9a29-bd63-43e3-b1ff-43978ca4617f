#!/bin/bash

# vLLM 服务器启动脚本
# 使用 Qwen2.5-7B-Instruct 模型

echo "启动 vLLM 服务器..."
echo "模型: Qwen2.5-7B-Instruct"
echo "端口: 9000"
echo "API Key: muyu"
echo "张量并行: 2"
echo "================================"

# 检查模型路径是否存在
MODEL_PATH="Qwen2.5-7B-Instruct"
if [ ! -d "$MODEL_PATH" ] && [ ! -f "$MODEL_PATH" ]; then
    echo "警告: 模型路径 $MODEL_PATH 可能不存在"
    echo "请确保模型已正确下载"
fi

# 启动服务器
vllm serve Qwen2.5-7B-Instruct \
  --served-model-name qwen2.5-7b \
  --api_key muyu \
  --host 0.0.0.0 \
  --port 9000 \
  --trust_remote_code \
  --tensor_parallel_size 2 \
  --max_model_len 8192 \
  --gpu_memory_utilization 0.9

echo "服务器已启动！"
echo "访问地址: http://**************:9000"
echo "API 文档: http://**************:9000/docs"
