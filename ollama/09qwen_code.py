import os

from openai import OpenAI

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key='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',  # 从环境变量获取 API 密钥，避免直接暴露在代码中
)

stream = client.chat.completions.create(
    model="qwen/qwen3-coder:free",
    messages=[
        {'role': 'system',
         'content': 'You are a helpful assistant.'
        },
        {'role': 'user',
         'content': '使用ros2编写一个简单的节点'
         }
    ],
    stream=True,
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end='', flush=True)

