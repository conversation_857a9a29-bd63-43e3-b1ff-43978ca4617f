import os
import time
from openai import OpenAI

def create_client():
    """创建 OpenRouter 客户端"""
    return OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key='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',
    )

def make_request_with_retry(client, max_retries=3):
    """带重试机制的请求"""
    for attempt in range(max_retries):
        try:
            print(f"尝试请求 {attempt + 1}/{max_retries}")

            stream = client.chat.completions.create(
                model="qwen/qwen3-coder:free",
                messages=[
                    {'role': 'system',
                     'content': 'You are a helpful assistant.'
                    },
                    {'role': 'user',
                     'content': '使用ros2编写一个简单的节点'
                     }
                ],
                stream=True,
                # 添加一些参数来避免限制
                max_tokens=1000,
                temperature=0.7,
            )

            print("请求成功，开始接收响应...")
            response_text = ""

            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    print(content, end='', flush=True)
                    response_text += content

            print(f"\n\n响应完成，总长度: {len(response_text)} 字符")
            return True

        except Exception as e:
            print(f"\n请求失败 (尝试 {attempt + 1}): {e}")
            if "rate limit" in str(e).lower():
                wait_time = (attempt + 1) * 10  # 递增等待时间
                print(f"检测到速率限制，等待 {wait_time} 秒...")
                time.sleep(wait_time)
            elif "quota" in str(e).lower():
                print("检测到配额限制，请检查账户状态")
                return False
            else:
                time.sleep(2)  # 其他错误等待2秒

    print("所有重试都失败了")
    return False

def main():
    """主函数"""
    print("OpenRouter API 测试")
    print("=" * 40)

    client = create_client()
    success = make_request_with_retry(client)

    if not success:
        print("\n建议检查:")
        print("1. API Key 是否有效")
        print("2. 账户是否有足够配额")
        print("3. 是否触发了速率限制")
        print("4. 网络连接是否正常")

if __name__ == "__main__":
    main()

