import requests  # type: ignore
import json

# 设置 API 端点
generate_url = "http://localhost:11434/api/chat"

# 示例数据
generate_payload = {
    "model": "deepseek-r1:7b",
    "messages": [
        {
            "role": "user",
            "content": "请生成一个关于人工智能的简短介绍，并思考一下人工智能的未来"
        }
    ],
    "stream": True,  # 启用流式输出
    "options": {
        "temperature": 0.6,
        "keep_alive": "10s",
        "num_ctx": 1
    }
}

# 调用生成接口
with requests.post(generate_url, json=generate_payload, stream=True) as response_generate:
    if response_generate.status_code == 200:
        # 逐行读取流式响应
        for line in response_generate.iter_lines():
            if line:  # 确保行不为空
                # 解析 JSON 响应
                generate_response = json.loads(line)
                
                # 提取并打印消息内容
                if "message" in generate_response and "content" in generate_response["message"]:
                    print(generate_response["message"]["content"], end='')  # end='' 防止换行
                elif "response" in generate_response:
                    print(generate_response["response"], end='')  # end='' 防止换行
                
                if generate_response.get("done", False):
                    break  # 如果 done 为 True，结束循环
    else:
        print("生成请求失败:", response_generate.status_code, response_generate.text)