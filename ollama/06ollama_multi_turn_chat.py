import requests  # type: ignore
import json

def chat_with_ollama(messages, model="deepseek-r1:7b"):
    """
    与 Ollama 进行单次对话
    
    Args:
        messages: 消息历史列表
        model: 使用的模型名称
    
    Returns:
        完整的助手回复内容
    """
    url = "http://localhost:11434/api/chat"
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": True,
        "options": {
            "temperature": 0.6,
            "keep_alive": "10m"
        }
    }
    
    assistant_response = ""
    
    try:
        with requests.post(url, json=payload, stream=True) as response:
            if response.status_code == 200:
                print("助手: ", end='', flush=True)
                
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            
                            # 提取消息内容
                            if "message" in data and "content" in data["message"]:
                                content = data["message"]["content"]
                                print(content, end='', flush=True)
                                assistant_response += content
                            elif "response" in data:
                                content = data["response"]
                                print(content, end='', flush=True)
                                assistant_response += content
                                
                            if data.get("done", False):
                                break
                                
                        except json.JSONDecodeError:
                            continue
                            
                print()  # 换行
                return assistant_response
            else:
                print(f"请求失败: {response.status_code} - {response.text}")
                return None
                
    except requests.RequestException as e:
        print(f"网络请求错误: {e}")
        return None

def print_conversation_history(messages):
    """
    打印对话历史
    """
    print("\n=== 对话历史 ===")
    for i, msg in enumerate(messages, 1):
        role = "用户" if msg["role"] == "user" else "助手"
        content = msg["content"]
        # 限制显示长度
        if len(content) > 100:
            content = content[:100] + "..."
        print(f"{i}. {role}: {content}")
    print("=" * 20)

def main():
    """
    主函数：实现多轮对话
    """
    print("=== Ollama 多轮对话 ===")
    print("输入 'quit' 或 'exit' 退出对话")
    print("输入 'clear' 清空对话历史")
    print("输入 'history' 查看对话历史")
    print("-" * 30)
    
    # 初始化消息历史
    messages = []
    
    while True:
        # 获取用户输入
        user_input = input("\n用户: ").strip()
        
        # 检查退出命令
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break
            
        # 检查清空命令
        if user_input.lower() in ['clear', '清空']:
            messages = []
            print("对话历史已清空")
            continue
            
        # 检查历史命令
        if user_input.lower() in ['history', '历史']:
            if messages:
                print_conversation_history(messages)
            else:
                print("暂无对话历史")
            continue
            
        # 检查空输入
        if not user_input:
            print("请输入有效内容")
            continue
            
        # 添加用户消息到历史
        messages.append({
            "role": "user",
            "content": user_input
        })
        
        # 获取助手回复
        assistant_reply = chat_with_ollama(messages)
        
        if assistant_reply:
            # 添加助手回复到历史
            messages.append({
                "role": "assistant", 
                "content": assistant_reply
            })
        else:
            # 如果请求失败，移除刚添加的用户消息
            messages.pop()
            print("获取回复失败，请重试")

if __name__ == "__main__":
    main()
