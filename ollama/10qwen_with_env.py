import os
from openai import OpenAI

# 方法1: 使用环境变量（推荐）
def create_client_with_env():
    """使用环境变量创建客户端"""
    api_key = os.getenv("QWEN_API_KEY")  # 从环境变量获取 API 密钥
    
    if not api_key:
        raise ValueError("请设置环境变量 QWEN_API_KEY")
    
    return OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

# 方法2: 直接设置 API 密钥（简单但不够安全）
def create_client_direct():
    """直接设置 API 密钥"""
    return OpenAI(
        api_key="sk-2cc00b422c5846d5b4e182b75fe522c0",  # 直接设置
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

# 方法3: 从文件读取 API 密钥
def create_client_from_file():
    """从文件读取 API 密钥"""
    try:
        with open("api_key.txt", "r") as f:
            api_key = f.read().strip()
        
        return OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
    except FileNotFoundError:
        raise FileNotFoundError("请创建 api_key.txt 文件并在其中放入您的 API 密钥")

def main():
    """主函数"""
    try:
        # 尝试使用环境变量
        try:
            client = create_client_with_env()
            print("✓ 使用环境变量创建客户端")
        except ValueError:
            # 如果环境变量不存在，使用直接设置的方式
            client = create_client_direct()
            print("✓ 使用直接设置的 API 密钥")
        
        # 发送请求
        completion = client.chat.completions.create(
            model="qwen3-coder-plus",
            messages=[
                {'role': 'system', 'content': 'You are a helpful assistant.'},
                {'role': 'user', 'content': '请编写一个Python函数 find_prime_numbers，该函数接受一个整数 n 作为参数，并返回一个包含所有小于 n 的质数（素数）的列表。质数是指仅能被1和其自身整除的正整数，如2, 3, 5, 7等。不要输出非代码的内容。'}
            ],
        )
        
        print("\n=== 生成的代码 ===")
        print(completion.choices[0].message.content)
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
