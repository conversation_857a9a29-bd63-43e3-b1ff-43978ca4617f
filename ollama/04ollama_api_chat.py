import requests
import json

chat_url = "http://localhost:11434/api/chat"

chat_payload = {
    "model": "deepseek-r1:7b",
    "messages": [
        {
            "role": "user",
            "content": "你好, 请你介绍一下你自己"
        }
    ],
    "tools": [],
    "stream": False
}

response_chat = requests.post(chat_url, json=chat_payload)
if response_chat.status_code == 200:
    chat_response = response_chat.json()
    # 提取 <think> 标签中的内容
    think_start = chat_response["message"]['content'].find("<think>")
    think_end = chat_response["message"]['content'].find("</think>")

    if think_start != -1 and think_end != -1:
        think_content = chat_response["message"]['content'][think_start + len("<think>"):think_end].strip()
    else:
        think_content = "No think content found."

    # 提取正常的文本内容
    normal_content = chat_response["message"]['content'][think_end + len("</think>"):].strip()

    # 打印结果
    print("思考内容:\n", think_content)
    print("\n正常内容:\n", normal_content)# 提取 <think> 标签中的内容
    think_start = chat_response["message"]['content'].find("<think>")
    think_end = chat_response["message"]['content'].find("</think>")
else:
    print("聊天请求失败:", response_chat.status_code, response_chat.text)
