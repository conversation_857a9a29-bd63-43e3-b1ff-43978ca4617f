import requests
import json

def simple_multi_turn_chat():
    """
    简单的多轮对话示例
    """
    url = "http://localhost:11434/api/chat"
    
    # 初始化对话历史
    messages = [
        {"role": "user", "content": "你好，我想学习Python"},
        {"role": "assistant", "content": "你好！我很乐意帮助你学习Python。Python是一门非常适合初学者的编程语言。你想从哪个方面开始学习呢？"},
        {"role": "user", "content": "我想了解Python的基本语法"}
    ]
    
    payload = {
        "model": "deepseek-r1:7b",
        "messages": messages,
        "stream": True,
        "options": {
            "temperature": 0.6,
            "keep_alive": "10m"
        }
    }
    
    print("=== 多轮对话示例 ===")
    print("对话历史:")
    for i, msg in enumerate(messages, 1):
        role = "用户" if msg["role"] == "user" else "助手"
        print(f"{i}. {role}: {msg['content']}")
    
    print("\n当前回复:")
    print("助手: ", end='', flush=True)
    
    try:
        with requests.post(url, json=payload, stream=True) as response:
            if response.status_code == 200:
                assistant_response = ""
                
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            
                            if "message" in data and "content" in data["message"]:
                                content = data["message"]["content"]
                                print(content, end='', flush=True)
                                assistant_response += content
                            elif "response" in data:
                                content = data["response"]
                                print(content, end='', flush=True)
                                assistant_response += content
                                
                            if data.get("done", False):
                                break
                                
                        except json.JSONDecodeError:
                            continue
                
                print("\n")
                
                # 显示完整的对话历史（包括新回复）
                messages.append({"role": "assistant", "content": assistant_response})
                
                print("\n=== 完整对话历史 ===")
                for i, msg in enumerate(messages, 1):
                    role = "用户" if msg["role"] == "user" else "助手"
                    content = msg['content']
                    if len(content) > 100:
                        content = content[:100] + "..."
                    print(f"{i}. {role}: {content}")
                    
            else:
                print(f"请求失败: {response.status_code}")
                
    except requests.RequestException as e:
        print(f"网络错误: {e}")

if __name__ == "__main__":
    simple_multi_turn_chat()
