import requests
import json

# 设置 api 端点
generate_url = "http://localhost:11434/api/generate"

# 实例数据
generate_payload = {
    "model": "deepseek-r1:7b",
    "prompt": "你好, 请你介绍一下你自己",
    "stream": False,
    "options": {
        # "num_ctx": 1,
        "num_predict": 10,
    }
}

# 调用生成接口
response_generate = requests.post(
    generate_url,
    json=generate_payload
)
if response_generate.status_code == 200:
    generate_response = response_generate.json()
    print("生成响应:", json.dumps(generate_response, ensure_ascii=False, indent=2))

    think_start = generate_response['response'].find("<think>")
    think_end = generate_response['response'].find("</think>")

    # 提取思考内容
    if think_start != -1 and think_end != -1:
        print("思考内容:", generate_response['response'][think_start + len("<think>"):think_end].strip())
    else:
        print("没有思考内容")

    # 提取正常的文本内容
    normal_content = generate_response['response'][think_end + len("</think>"):].strip()
    print("正常内容:", normal_content)
    print("总响应时间:", generate_response['total_duration'] / 10**9, "秒")
    print("加载模型时间:", generate_response['load_duration'] / 10**9, "秒")
    print("提示 token 数量:", generate_response['prompt_eval_count'])
    print("提示评估时间:", generate_response['prompt_eval_duration'] / 10**9, "秒")
    print("响应 token 数量:", generate_response['eval_count'])
    print("响应生成时间:", generate_response['eval_duration'] / 10**9, "秒")
    print("对话上下文编码:", generate_response['context'])
    print("生成的文本内容:", normal_content)
else:
    print("生成请求失败:", response_generate.status_code, response_generate.text)


# -----------------------请求成功返回参数----------------
# total_duration：单次响应花费的总时间
# load_duration：加载模型花费的时间
# prompt_eval_count: 提示中的 token 数量
# prompt_eval_duration: 评估提示所花费的时间
# eval_count: 响应中的 token 数量
# eval_duration: 生成响应所花费的时间
# context: 在此响应中使用的对话的编码，可以在下一个请求中发送以保持对话记忆
# response: 生成的文本内容。如果输出为空，表示使用流式输出