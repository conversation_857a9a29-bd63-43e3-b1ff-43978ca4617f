import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig
import time
import re


def load_model_and_tokenizer():
    """加载模型和分词器"""
    model_path = "/home/<USER>/wangsaner/Weights/Qwen/Qwen3-0.6B"
    
    print("正在加载模型和分词器...")
    
    # 加载分词器
    tokenizer = AutoTokenizer.from_pretrained(
        model_path, 
        trust_remote_code=True,
        padding_side="left"
    )
    
    # 设置pad_token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # 加载模型
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        low_cpu_mem_usage=True
    )
    
    print("模型和分词器加载完成")
    return model, tokenizer


def create_generation_config():
    """创建生成配置，防止重复"""
    return GenerationConfig(
        max_new_tokens=200,
        min_new_tokens=30,
        temperature=0.8,
        top_p=0.9,
        top_k=50,
        repetition_penalty=1.2,
        no_repeat_ngram_size=3,  # 防止3-gram重复
        do_sample=True,
        pad_token_id=151643,  # Qwen的pad_token_id
        eos_token_id=151645,  # Qwen的eos_token_id
        early_stopping=True,
    )


def clean_output(text, original_prompt):
    """清理输出文本"""
    # 移除原始提示
    if original_prompt in text:
        text = text.replace(original_prompt, "").strip()
    
    # 移除常见的无效开头
    text = re.sub(r'^[，。\s]*', '', text)
    
    # 移除重复的句子
    sentences = text.split('。')
    unique_sentences = []
    seen = set()
    
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and sentence not in seen and len(sentence) > 5:
            unique_sentences.append(sentence)
            seen.add(sentence)
    
    result = '。'.join(unique_sentences)
    if result and not result.endswith('。'):
        result += '。'
    
    return result


def generate_text(model, tokenizer, prompt, generation_config):
    """生成文本"""
    # 编码输入
    inputs = tokenizer(prompt, return_tensors="pt", padding=True)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    # 生成
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            generation_config=generation_config,
            pad_token_id=tokenizer.pad_token_id,
        )
    
    # 解码输出
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # 清理输出
    cleaned_text = clean_output(generated_text, prompt)
    
    return generated_text, cleaned_text


def main():
    """主函数"""
    print("="*60)
    print("Transformers 推理 - 防重复版本")
    print("="*60)
    
    try:
        # 加载模型
        model, tokenizer = load_model_and_tokenizer()
        
        # 创建生成配置
        generation_config = create_generation_config()
        
        # 测试提示词
        prompts = [
            "黑洞是什么？",
            "请解释黑洞的基本概念。",
            "用简单的话说明什么是黑洞。"
        ]
        
        for i, prompt in enumerate(prompts, 1):
            print(f"\n--- 测试 {i}: {prompt} ---")
            
            start_time = time.time()
            raw_output, cleaned_output = generate_text(model, tokenizer, prompt, generation_config)
            generation_time = time.time() - start_time
            
            print(f"生成时间: {generation_time:.2f}秒")
            print(f"原始输出长度: {len(raw_output)} 字符")
            print(f"清理后长度: {len(cleaned_output)} 字符")
            print(f"原始输出: {raw_output}")
            print(f"清理后输出: {cleaned_output}")
            
            # 质量评估
            if len(cleaned_output) > 20 and any(word in cleaned_output for word in ["黑洞", "引力", "天体", "宇宙"]):
                print("✅ 输出质量: 良好")
            else:
                print("⚠️  输出质量: 需要改进")
            
            print("-" * 50)
    
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("\n可能的解决方案:")
        print("1. 检查模型路径是否正确")
        print("2. 确保有足够的GPU内存")
        print("3. 尝试使用CPU推理 (device_map='cpu')")
    
    print("\n" + "="*60)
    print("测试完成")
    print("="*60)


if __name__ == "__main__":
    main()
